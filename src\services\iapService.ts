import {
  initConnection,
  endConnection,
  getProducts,
  requestPurchase,
  finishTransaction,
  acknowledgePurchaseAndroid,
  consumePurchaseAndroid,
  getAvailablePurchases,
  Product as RNIAPProduct,
  Purchase as RNIAPPurchase,
  PurchaseError,
} from 'react-native-iap';
import { Platform } from 'react-native';
import { PRODUCT_IDS, Product, Purchase } from '../types/iap';

class IAPService {
  private isInitialized = false;

  async initialize(): Promise<boolean> {
    try {
      const result = await initConnection();
      this.isInitialized = result;
      console.log('IAP Connection initialized:', result);
      return result;
    } catch (error) {
      console.error('Failed to initialize IAP connection:', error);
      return false;
    }
  }

  async disconnect(): Promise<void> {
    try {
      await endConnection();
      this.isInitialized = false;
      console.log('IAP Connection ended');
    } catch (error) {
      console.error('Failed to end IAP connection:', error);
    }
  }

  async getProducts(): Promise<Product[]> {
    if (!this.isInitialized) {
      throw new Error('IAP not initialized');
    }

    try {
      const productIds = Object.values(PRODUCT_IDS);
      const products = await getProducts({ skus: productIds });
      
      return products.map((product: RNIAPProduct) => ({
        productId: product.productId,
        price: product.price,
        currency: product.currency,
        title: product.title,
        description: product.description,
        localizedPrice: product.localizedPrice,
      }));
    } catch (error) {
      console.error('Failed to get products:', error);
      throw error;
    }
  }

  async purchaseProduct(productId: string): Promise<Purchase> {
    if (!this.isInitialized) {
      throw new Error('IAP not initialized');
    }

    try {
      const purchase = await requestPurchase({ sku: productId });
      console.log('Purchase successful:', purchase);
      
      // Convert to our Purchase type
      const result: Purchase = {
        productId: purchase.productId,
        transactionId: purchase.transactionId,
        transactionDate: purchase.transactionDate,
        transactionReceipt: purchase.transactionReceipt,
        purchaseToken: purchase.purchaseToken,
        dataAndroid: purchase.dataAndroid,
        signatureAndroid: purchase.signatureAndroid,
        isAcknowledgedAndroid: purchase.isAcknowledgedAndroid,
        purchaseStateAndroid: purchase.purchaseStateAndroid,
        developerPayloadAndroid: purchase.developerPayloadAndroid,
        originalTransactionDateIOS: purchase.originalTransactionDateIOS,
        originalTransactionIdentifierIOS: purchase.originalTransactionIdentifierIOS,
      };

      return result;
    } catch (error) {
      console.error('Purchase failed:', error);
      throw error;
    }
  }

  async consumePurchase(purchase: Purchase): Promise<void> {
    try {
      if (Platform.OS === 'android') {
        // For Android, we need to acknowledge and consume the purchase
        if (purchase.purchaseToken && !purchase.isAcknowledgedAndroid) {
          await acknowledgePurchaseAndroid({
            token: purchase.purchaseToken,
            developerPayload: purchase.developerPayloadAndroid,
          });
        }

        if (purchase.purchaseToken) {
          await consumePurchaseAndroid({
            token: purchase.purchaseToken,
            developerPayload: purchase.developerPayloadAndroid,
          });
        }
      } else {
        // For iOS, finish the transaction
        await finishTransaction({
          purchase: purchase as any,
          isConsumable: true,
        });
      }
      
      console.log('Purchase consumed successfully');
    } catch (error) {
      console.error('Failed to consume purchase:', error);
      throw error;
    }
  }

  async restorePurchases(): Promise<Purchase[]> {
    if (!this.isInitialized) {
      throw new Error('IAP not initialized');
    }

    try {
      const purchases = await getAvailablePurchases();
      
      return purchases.map((purchase: RNIAPPurchase) => ({
        productId: purchase.productId,
        transactionId: purchase.transactionId,
        transactionDate: purchase.transactionDate,
        transactionReceipt: purchase.transactionReceipt,
        purchaseToken: purchase.purchaseToken,
        dataAndroid: purchase.dataAndroid,
        signatureAndroid: purchase.signatureAndroid,
        isAcknowledgedAndroid: purchase.isAcknowledgedAndroid,
        purchaseStateAndroid: purchase.purchaseStateAndroid,
        developerPayloadAndroid: purchase.developerPayloadAndroid,
        originalTransactionDateIOS: purchase.originalTransactionDateIOS,
        originalTransactionIdentifierIOS: purchase.originalTransactionIdentifierIOS,
      }));
    } catch (error) {
      console.error('Failed to restore purchases:', error);
      throw error;
    }
  }

  isConnected(): boolean {
    return this.isInitialized;
  }
}

export const iapService = new IAPService();
